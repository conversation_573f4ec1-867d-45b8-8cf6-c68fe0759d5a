import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PdfService {
  /// التحقق من الأذونات المطلوبة حسب إصدار Android
  Future<bool> _requestStoragePermissions() async {
    if (!Platform.isAndroid) {
      return true; // iOS لا يحتاج أذونات خاصة
    }

    try {
      // التحقق من إصدار Android
      final isAndroid13Plus = await _isAndroid13OrHigher();
      
      if (isAndroid13Plus) {
        // Android 13+ يستخدم Storage Access Framework
        // لا نحتاج أذونات خاصة - file_picker سيتعامل مع كل شيء
        print('Android 13+ detected - using Storage Access Framework');
        return true;
      } else {
        // Android 12 وما دون - نحتاج READ_EXTERNAL_STORAGE فقط
        print('Android 12 or below - requesting storage permission');
        var status = await Permission.storage.status;
        
        if (!status.isGranted) {
          status = await Permission.storage.request();
        }
        
        if (status.isPermanentlyDenied) {
          print('Storage permission permanently denied');
          await openAppSettings();
          return false;
        }
        
        return status.isGranted;
      }
    } catch (e) {
      print('Error checking permissions: $e');
      // في حالة الخطأ، نجرب المتابعة
      return true;
    }
  }

  /// التحقق من إصدار Android
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 = API 33
    } catch (e) {
      print('Error getting Android version: $e');
      return false;
    }
  }

  /// اختيار ملف PDF من الجهاز
  Future<Map<String, dynamic>?> pickPdfFile() async {
    try {
      print('Starting PDF file selection...');

      // 1. التحقق من الأذونات
      final hasPermission = await _requestStoragePermissions();
      if (!hasPermission) {
        throw Exception(
          'إذن الوصول إلى التخزين مطلوب لاختيار الملفات. '
          'يرجى منح الإذن من إعدادات التطبيق.'
        );
      }

      print('Permissions granted, opening file picker...');

      // 2. فتح منتقي الملفات
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        dialogTitle: 'اختر ملف PDF',
        // إعدادات إضافية لـ Android
        withData: false, // تحسين الأداء
        withReadStream: false,
      );

      if (result == null || result.files.isEmpty) {
        print('User cancelled file selection');
        return null;
      }

      final platformFile = result.files.first;
      final filePath = platformFile.path;

      if (filePath == null) {
        print('Error: File path is null');
        throw Exception(
          'لم يتم الحصول على مسار الملف. يرجى المحاولة مرة أخرى.'
        );
      }

      print('Selected file path: $filePath');

      // 3. التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        print('Error: File does not exist at path: $filePath');
        throw Exception(
          'لم يتم العثور على الملف المحدد. يرجى المحاولة مرة أخرى.'
        );
      }

      // 4. التحقق من حجم الملف
      final fileSize = await file.length();
      print('File size: ${fileSize} bytes');

      // حد أقصى 50 ميجابايت
      if (fileSize > 50 * 1024 * 1024) {
        throw Exception(
          'حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 50 ميجابايت.'
        );
      }

      print('File validation successful');
      return {
        'file': file,
        'fileName': platformFile.name,
        'fileSize': fileSize,
      };

    } catch (e) {
      print('Error in pickPdfFile: $e');
      
      // إعادة رمي الأخطاء المعروفة
      if (e is Exception) {
        rethrow;
      }
      
      // خطأ عام
      throw Exception(
        'فشل في اختيار ملف PDF. يرجى التأكد من اختيار ملف صالح.'
      );
    }
  }
  
  /// استخراج النص من ملف PDF
  Future<String> extractTextFromPdf(File file) async {
    try {
      print('Starting text extraction from PDF...');

      // التحقق من وجود الملف
      if (!await file.exists()) {
        print('Error: PDF file does not exist at path: ${file.path}');
        throw Exception('ملف PDF المحدد غير موجود.');
      }

      // قراءة بيانات الملف
      print('Reading PDF file bytes...');
      final bytes = await file.readAsBytes();
      
      if (bytes.isEmpty) {
        throw Exception('الملف فارغ أو تالف.');
      }

      // إنشاء مستند PDF
      print('Creating PDF document...');
      final document = PdfDocument(inputBytes: bytes);
      
      if (document.pages.count == 0) {
        document.dispose();
        throw Exception('ملف PDF لا يحتوي على صفحات.');
      }

      // استخراج النص
      print('Extracting text from ${document.pages.count} pages...');
      final extractor = PdfTextExtractor(document);
      
      final buffer = StringBuffer();
      
      // استخراج النص من كل صفحة
      for (int i = 0; i < document.pages.count; i++) {
        try {
          final pageText = extractor.extractText(
            startPageIndex: i, 
            endPageIndex: i
          );
          
          if (pageText.trim().isNotEmpty) {
            buffer.write(pageText.trim());
            buffer.write('\n\n');
          }
          
          print('Extracted text from page ${i + 1}');
        } catch (e) {
          print('Error extracting text from page ${i + 1}: $e');
          // متابعة مع الصفحات الأخرى
        }
      }
      
      // تحرير المستند
      document.dispose();
      
      String extractedText = buffer.toString().trim();
      
      if (extractedText.isEmpty) {
        throw Exception(
          'لم يتم العثور على نص في ملف PDF. '
          'قد يكون الملف يحتوي على صور فقط أو محمي بكلمة مرور.'
        );
      }

      // تحديد طول النص لتجنب قيود واجهة برمجة التطبيقات
      if (extractedText.length > 15000) {
        print('Text too long, truncating to 15000 characters');
        extractedText = extractedText.substring(0, 15000) + '\n\n[تم اقتطاع النص...]';
      }
      
      print('Text extraction completed. Length: ${extractedText.length} characters');
      return extractedText;
      
    } catch (e) {
      print('Error in extractTextFromPdf: $e');
      
      if (e is Exception) {
        rethrow;
      }
      
      throw Exception('فشل في استخراج النص من ملف PDF: ${e.toString()}');
    }
  }
}
