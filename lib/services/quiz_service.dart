import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz_model.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

class QuizService {
  // حفظ الاختبارات في الذاكرة المحلية
  Future<void> saveQuizzes(List<Quiz> quizzes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = quizzes
          .map((quiz) => jsonEncode(quiz.toJson()))
          .toList();
      
      await prefs.setStringList('quizzes', quizzesJson);
    } catch (e) {
      print('Error saving quizzes: $e');
      throw Exception('فشل في حفظ الاختبارات: $e');
    }
  }
  
  // استرجاع الاختبارات من الذاكرة المحلية
  Future<List<Quiz>> loadQuizzes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = prefs.getStringList('quizzes') ?? [];
      
      return quizzesJson
          .map((json) => Quiz.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('Error loading quizzes: $e');
      return [];
    }
  }
  
  // إنشاء الاختبارات باستخدام الذكاء الاصطناعي
  Future<List<Question>> generateQuizQuestions(
    String pdfText, 
    int questionCount, 
    QuizDifficulty difficulty
  ) async {
    try {
      final model = GenerativeModel(
        model: 'gemini-2.0-flash',
        apiKey: 'AIzaSyBExV2C3FZSlmMmakl0vBre0PvkMlqPpfU',
      );
      
      // تحديد مستوى الصعوبة في الطلب إلى الذكاء الاصطناعي
      String difficultyText = '';
      switch (difficulty) {
        case QuizDifficulty.easy:
          difficultyText = 'سهلة وبسيطة تناسب المبتدئين';
          break;
        case QuizDifficulty.medium:
          difficultyText = 'متوسطة الصعوبة تناسب المستوى المتوسط';
          break;
        case QuizDifficulty.hard:
          difficultyText = 'صعبة ومتقدمة تتطلب معرفة عميقة بالموضوع';
          break;
      }
      
      final prompt = '''
أنشئ اختبارًا تفاعليًا بناءً على المحتوى التالي. قم بإنشاء $questionCount أسئلة متعددة الخيارات مع 4 خيارات لكل سؤال.
الأسئلة يجب أن تكون بمستوى $difficultyText.

قم بتنسيق إجابتك بتنسيق JSON بالضبط كما هو موضح في المثال. تأكد من أن الإجابة الصحيحة هي دائمًا خيار واحد فقط (0 أو 1 أو 2 أو 3).

المحتوى:
$pdfText

أريد الإجابة بهذا التنسيق بالضبط (JSON فقط، بدون أي نص إضافي):
[
  {
    "question": "نص السؤال الأول هنا؟",
    "options": ["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"],
    "correctAnswerIndex": 0
  },
  {
    "question": "نص السؤال الثاني هنا؟",
    "options": ["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"],
    "correctAnswerIndex": 2
  }
]
''';

      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);
      
      final jsonResponse = response.text;
      if (jsonResponse == null || jsonResponse.isEmpty) {
        throw Exception('فشل في إنشاء الأسئلة');
      }
      
      // تنظيف نص JSON (إزالة أي محتوى غير JSON)
      String cleanedJson = jsonResponse.trim();
      // إزالة علامات كتلة التعليمات البرمجية
      if (cleanedJson.startsWith('```json')) {
        cleanedJson = cleanedJson.substring(7);
      }
      if (cleanedJson.startsWith('```')) {
        cleanedJson = cleanedJson.substring(3);
      }
      if (cleanedJson.endsWith('```')) {
        cleanedJson = cleanedJson.substring(0, cleanedJson.length - 3);
      }
      cleanedJson = cleanedJson.trim();
      
      // تحليل استجابة JSON
      List<dynamic> questionsJson = jsonDecode(cleanedJson);
      
      // التحويل إلى كائنات Question
      List<Question> questions = questionsJson.map((q) {
        return Question(
          question: q['question'],
          options: List<String>.from(q['options']),
          correctAnswerIndex: q['correctAnswerIndex'],
        );
      }).toList();
      
      return questions;
    } catch (e) {
      print('Error generating quiz: $e');
      throw Exception('فشل في إنشاء الاختبار: $e');
    }
  }
}
