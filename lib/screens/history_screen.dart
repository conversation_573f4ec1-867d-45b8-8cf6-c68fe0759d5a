import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../main.dart'; // Import for AppNavigation
import '../models/quiz_model.dart';
import 'quiz_taking_screen.dart';

class HistoryScreen extends StatefulWidget {
  @override
  _HistoryScreenState createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  List<Quiz> quizHistory = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadQuizHistory();
  }

  Future<void> _loadQuizHistory() async {
    setState(() {
      isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = prefs.getStringList('quizzes') ?? [];
      
      setState(() {
        quizHistory = quizzesJson
            .map((json) => Quiz.fromJson(jsonDecode(json)))
            .toList();
        
        // Sort by date - newest first
        quizHistory.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        isLoading = false;
      });
    } catch (e) {
      print('Error loading quiz history: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _deleteQuiz(Quiz quiz) async {
    try {
      setState(() {
        quizHistory.remove(quiz);
      });

      // Save updated list
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = quizHistory
          .map((q) => jsonEncode(q.toJson()))
          .toList();
      
      await prefs.setStringList('quizzes', quizzesJson);
    } catch (e) {
      print('Error deleting quiz: $e');
      // Revert changes if error
      _loadQuizHistory();
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  "سجل الاختبارات",
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  "اختباراتك السابقة",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF30BEA2),
                  ),
                ),
                SizedBox(height: 20),
                Expanded(
                  child: isLoading
                      ? Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF30BEA2),
                          ),
                        )
                      : quizHistory.isEmpty
                          ? _buildEmptyHistory()
                          : ListView.builder(
                              itemCount: quizHistory.length,
                              itemBuilder: (context, index) {
                                final quiz = quizHistory[index];
                                return Dismissible(
                                  key: Key(quiz.title + quiz.createdAt.toString()),
                                  direction: DismissDirection.endToStart,
                                  background: Container(
                                    alignment: Alignment.centerLeft,
                                    padding: EdgeInsets.symmetric(horizontal: 20),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Icon(
                                      Icons.delete,
                                      color: Colors.white,
                                    ),
                                  ),
                                  onDismissed: (direction) {
                                    _deleteQuiz(quiz);
                                  },
                                  confirmDismiss: (direction) async {
                                    return await showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return AlertDialog(
                                          title: Text("تأكيد الحذف"),
                                          content: Text(
                                              "هل أنت متأكد من حذف هذا الاختبار؟"),
                                          actions: <Widget>[
                                            TextButton(
                                              child: Text("إلغاء"),
                                              onPressed: () => Navigator.of(context).pop(false),
                                            ),
                                            TextButton(
                                              child: Text("حذف"),
                                              onPressed: () => Navigator.of(context).pop(true),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.only(bottom: 15.0),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(15),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.grey.withOpacity(0.1),
                                            spreadRadius: 1,
                                            blurRadius: 3,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: ListTile(
                                        contentPadding: EdgeInsets.all(15),
                                        title: Text(
                                          quiz.title,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        subtitle: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(height: 5),
                                            Text(
                                              'عدد الأسئلة: ${quiz.questions.length}',
                                              style: TextStyle(fontSize: 12),
                                            ),
                                            SizedBox(height: 3),
                                            Text(
                                              'تاريخ الإنشاء: ${_formatDate(quiz.createdAt)}',
                                              style: TextStyle(fontSize: 12),
                                            ),
                                          ],
                                        ),
                                        trailing: Icon(
                                          Icons.arrow_forward_ios,
                                          color: Color(0xFF30BEA2),
                                          size: 16,
                                        ),
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => QuizTakingScreen(quiz: quiz),
                                            ),
                                          );
                                        },
                                        leading: Container(
                                          padding: EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                            color: Color(0xFF30BEA2).withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                          child: Icon(
                                            Icons.history,
                                            color: Color(0xFF30BEA2),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
                if (quizHistory.isNotEmpty) 
                  SizedBox(height: 20),
                if (quizHistory.isNotEmpty)
                  OutlinedButton.icon(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text("تأكيد الحذف"),
                            content: Text(
                                "هل أنت متأكد من حذف جميع الاختبارات؟ لا يمكن التراجع عن هذا الإجراء."),
                            actions: <Widget>[
                              TextButton(
                                child: Text("إلغاء"),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                              TextButton(
                                child: Text("حذف الكل"),
                                onPressed: () async {
                                  Navigator.of(context).pop();
                                  final prefs = await SharedPreferences.getInstance();
                                  await prefs.setStringList('quizzes', []);
                                  _loadQuizHistory();
                                },
                              ),
                            ],
                          );
                        },
                      );
                    },
                    icon: Icon(Icons.delete_sweep, color: Colors.red),
                    label: Text(
                      "حذف جميع الاختبارات",
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.red),
                      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyHistory() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 80, color: Color(0xFF30BEA2)),
          SizedBox(height: 20),
          Text('سجل الاختبارات',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
          SizedBox(height: 20),
          Text('هنا يمكنك الوصول إلى جميع الاختبارات التي قمت بإنشائها',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14)),
          SizedBox(height: 20),
          Text('لا توجد اختبارات في السجل بعد. قم بإنشاء اختبار أولاً!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey)),
          SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: () {
              // Use the AppNavigation helper class to navigate to Quiz tab
              AppNavigation.navigateToQuizTab();
            },
            icon: Icon(Icons.add, color: Colors.white),
            label: Text(
              "إنشاء اختبار جديد",
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF30BEA2),
              padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
