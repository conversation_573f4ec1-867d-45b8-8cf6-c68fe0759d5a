import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 2),
    );
    
    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    
    _controller.forward();
    
    // تم إزالة التحويل التلقائي بعد انتهاء الانيميشن
  }
  
  // التحقق من حالة تسجيل دخول المستخدم والانتقال إلى الشاشة المناسبة
  void _checkUserAndNavigate() {
    // التحقق من حالة تسجيل دخول المستخدم
    final currentUser = FirebaseAuth.instance.currentUser;
    
    if (currentUser != null) {
      // إذا كان المستخدم مسجل الدخول، ننتقل إلى الصفحة الرئيسية
      Navigator.of(context).pushReplacementNamed('/home');
    } else {
      // إذا كان المستخدم غير مسجل، ننتقل إلى صفحة تسجيل الدخول
      Navigator.of(context).pushReplacementNamed('/auth');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: Color(0xFFF0FBF8),
      body: SafeArea(
        child: Directionality(
          textDirection: TextDirection.rtl, // Set RTL direction for Arabic support
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFF0FBF8),
                      Color(0xFFE0F5F0),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // App Name with reduced size
                          FadeTransition(
                            opacity: _fadeInAnimation,
                            child: ScaleTransition(
                              scale: _scaleAnimation,
                              child: Text(
                                "Quizimy",
                                style: TextStyle(
                                  fontSize: size.width * 0.15, // Reduced from 0.18
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF30BEA2),
                                  letterSpacing: 2.0,
                                  shadows: [
                                    Shadow(
                                      blurRadius: 8.0,
                                      color: Color(0xFF30BEA2).withOpacity(0.4),
                                      offset: Offset(0, 3),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          
                          SizedBox(height: 16),
                          
                          // Decorative Line - made slightly larger
                          FadeTransition(
                            opacity: _fadeInAnimation,
                            child: Container(
                              width: size.width * 0.5,
                              height: 5,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Color(0xFF30BEA2).withOpacity(0.2),
                                    Color(0xFF30BEA2),
                                    Color(0xFF30BEA2).withOpacity(0.2),
                                  ],
                                  begin: Alignment.centerRight,
                                  end: Alignment.centerLeft,
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          
                          SizedBox(height: 32),
                          
                          // App Description
                          FadeTransition(
                            opacity: _fadeInAnimation,
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 32),
                              child: Text(
                                "تطبيق تفاعلي لتحويل ملفات PDF إلى اختبارات MCQ",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF30BEA2).withOpacity(0.8),
                                  height: 1.5,
                                ),
                              ),
                            ),
                          ),
                          
                          SizedBox(height: 24),
                          
                          // Added catchy phrases
                          _buildCatchyPhrase(
                            "✨ تعلم بطريقة ذكية وفعالة",
                            _fadeInAnimation,
                          ),
                          
                          SizedBox(height: 12),
                          
                          _buildCatchyPhrase(
                            "📚 اختبارات مخصصة من محتواك الخاص",
                            _fadeInAnimation,
                          ),
                          
                          SizedBox(height: 12),
                          
                          _buildCatchyPhrase(
                            "🚀 وفر الوقت واستمتع بالتعلم",
                            _fadeInAnimation,
                          ),
                        ],
                      ),
                    ),
                    
                    // Start Button
                    FadeTransition(
                      opacity: _fadeInAnimation,
                      child: Padding(
                        padding: EdgeInsets.only(bottom: 40, left: 24, right: 24),
                        child: ElevatedButton(
                          onPressed: () {
                            _checkUserAndNavigate();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF30BEA2),
                            minimumSize: Size(double.infinity, 60),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                            elevation: 8,
                            shadowColor: Color(0xFF30BEA2).withOpacity(0.5),
                          ),
                          child: Text(
                            "ابدأ الآن",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
  
  // Helper method to create catchy phrases with consistent styling
  Widget _buildCatchyPhrase(String text, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Color(0xFF30BEA2).withOpacity(0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Color(0xFF2A8A79),
          ),
        ),
      ),
    );
  }
}
