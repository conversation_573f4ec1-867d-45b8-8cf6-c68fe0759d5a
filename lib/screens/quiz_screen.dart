import 'package:flutter/material.dart';
import '../models/quiz_model.dart';
import '../services/quiz_service.dart';
import '../services/premium_service.dart';
import 'quiz_settings_screen.dart';
import 'quiz_taking_screen.dart';
import 'history_screen.dart';
import 'subscription_screen.dart';

class QuizScreen extends StatefulWidget {
  @override
  _QuizScreenState createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen> {
  List<Quiz> quizzes = [];
  bool isLoading = true;
  final QuizService _quizService = QuizService();
  final PremiumService _premiumService = PremiumService();

  @override
  void initState() {
    super.initState();
    _loadQuizzes();
  }

  Future<void> _loadQuizzes() async {
    setState(() {
      isLoading = true;
    });

    try {
      final loadedQuizzes = await _quizService.loadQuizzes();

      setState(() {
        quizzes = loadedQuizzes;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading quizzes: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  // إضافة طريقة عرض إعدادات الأسئلة
  void _showQuizSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: AlertDialog(
            title: Text("إعدادات الأسئلة"),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListTile(
                  leading: Icon(Icons.format_list_numbered),
                  title: Text("عدد الأسئلة في الاختبار"),
                  subtitle: Text("10 أسئلة"),
                  onTap: () {
                    // يمكن إضافة منطق لتغيير عدد الأسئلة
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.timer),
                  title: Text("وقت الاختبار"),
                  subtitle: Text("غير محدود"),
                  onTap: () {
                    // يمكن إضافة منطق لضبط الوقت
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.language),
                  title: Text("لغة الأسئلة"),
                  subtitle: Text("العربية"),
                  onTap: () {
                    // يمكن إضافة منطق لتغيير اللغة
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text("إغلاق"),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _navigateToQuizSettings() async {
    // التحقق من إمكانية إنشاء اختبار جديد
    final canCreate = await _premiumService.canCreateQuiz();

    if (!canCreate) {
      // إذا لم يكن بإمكان المستخدم إنشاء اختبار، اعرض صفحة الاشتراك
      if (mounted) {
        _showSubscriptionScreen();
      }
      return;
    }

    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => QuizSettingsScreen(),
        ),
      ).then((_) {
        // إعادة تحميل الاختبارات عند العودة من شاشة الإعدادات
        _loadQuizzes();
      });
    }
  }

  void _navigateToQuizTaking(Quiz quiz) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuizTakingScreen(quiz: quiz),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // رأس الصفحة مع زخرفة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFF30BEA2),
                        Color(0xFF30BEA2).withOpacity(0.7)
                      ],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0xFF30BEA2).withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 0,
                        offset: Offset(0, 5),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "الاختبارات",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          // زر الأرشيف فقط
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: IconButton(
                              icon: Icon(Icons.history, color: Colors.white),
                              tooltip: "أرشيف الاختبارات",
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => HistoryScreen(),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10), // Adjusted spacing
                      Text(
                        "أنشئ اختبارات ذكية، معززة بالذكاء الاصطناعي.", // Updated AI-focused subtitle
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 20), // Space before the new button
                      // New "Create Quiz" button
                      ElevatedButton.icon(
                        onPressed: _navigateToQuizSettings,
                        icon: Icon(Icons.auto_awesome, color: Color(0xFF30BEA2)), // AI-themed icon
                        label: Text(
                          "إنشاء اختبار جديد",
                          style: TextStyle(
                            color: Color(0xFF30BEA2),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white, // Contrasting background
                          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          elevation: 5,
                          shadowColor: Colors.black.withOpacity(0.2),
                          minimumSize: Size(double.infinity, 48), // Make button wider
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 25),
                // قسم اختباراتك المحسّن (This is the one to keep)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Color(0xFF30BEA2).withOpacity(0.15),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(Icons.assignment_outlined,
                              color: Color(0xFF30BEA2), size: 22),
                        ),
                        SizedBox(width: 10),
                        Text(
                          "اختباراتك",
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    if (quizzes.isNotEmpty && !isLoading) // Show count if quizzes exist and not loading
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: Color(0xFF30BEA2).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          "${quizzes.length} اختبار${quizzes.length == 1 ? '' : 'ات'} ذكية",
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF30BEA2),
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 15), // This SizedBox remains to space before the GridView
                Expanded(
                  child: isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                color: Color(0xFF30BEA2),
                              ),
                              SizedBox(height: 20),
                              Text(
                                'لحظات... الذكاء الاصطناعي يجهز اختباراتك', // Updated loading text
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : quizzes.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(20),
                                    decoration: BoxDecoration(
                                      color:
                                          Color(0xFF30BEA2).withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(Icons.auto_awesome_outlined, // Updated empty state icon
                                        size: 70, color: Color(0xFF30BEA2)),
                                  ),
                                  SizedBox(height: 30),
                                  Text(
                                    'لم تقم بإنشاء أي اختبار بعد',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  SizedBox(height: 12),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 40),
                                    child: Text(
                                      "ابدأ بإنشاء اختبارك الذكي الأول. حمّل ملفك ودع الذكاء الاصطناعي يساعدك.", // Updated AI-focused empty state text
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                        height: 1.5,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                                childAspectRatio: 0.8,
                              ),
                              itemCount: quizzes.length,
                              itemBuilder: (context, index) {
                                final quiz = quizzes[index];
                                return GestureDetector(
                                  onTap: () => _navigateToQuizTaking(quiz),
                                  child: Card(
                                    elevation: 3,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(18),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: Color(0xFF30BEA2),
                                            borderRadius: BorderRadius.only(
                                              topRight: Radius.circular(18),
                                              topLeft: Radius.circular(18),
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Padding(
                                            padding: const EdgeInsets.all(12.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Align(
                                                  alignment: Alignment.center,
                                                  child: Container(
                                                    padding: EdgeInsets.all(12),
                                                    decoration: BoxDecoration(
                                                      color: Color(0xFF30BEA2)
                                                          .withOpacity(0.1),
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Icon(
                                                        Icons.auto_stories_outlined, // Updated card icon
                                                        color:
                                                            Color(0xFF30BEA2),
                                                        size: 28),
                                                  ),
                                                ),
                                                SizedBox(height: 12),
                                                Text(
                                                  quiz.title,
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  textAlign: TextAlign.center, // Center align title
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 15, // Slightly larger title
                                                    color: Colors.black87,
                                                  ),
                                                ),
                                                Spacer(),
                                                Divider(height: 1, thickness: 0.5),
                                                Padding(
                                                  padding: const EdgeInsets.only(top: 8.0), // Add padding above the row
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          Icon(
                                                              Icons.quiz_outlined, // Updated questions icon
                                                              color: Color(
                                                                  0xFF30BEA2),
                                                              size: 16), // Slightly larger icon
                                                          SizedBox(width: 5),
                                                          Text(
                                                            '${quiz.questions.length} سؤال', // Added "سؤال"
                                                            style: TextStyle(
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                color: Colors.grey[700]),
                                                          ),
                                                        ],
                                                      ),
                                                      Text(
                                                        _formatDate(
                                                            quiz.createdAt),
                                                        style: TextStyle(
                                                            fontSize: 11,
                                                            color: Colors
                                                                .grey[600]),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showSubscriptionScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubscriptionScreen(),
      ),
    );
  }
}
